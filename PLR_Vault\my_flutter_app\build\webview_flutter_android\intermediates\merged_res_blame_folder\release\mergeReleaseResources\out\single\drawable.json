[{"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-release-26:/drawable/notification_tile_bg.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-core-1.13.1-9:/drawable/notification_tile_bg.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-release-26:/drawable/notification_bg.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-core-1.13.1-9:/drawable/notification_bg.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-release-26:/drawable/notification_bg_low.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-core-1.13.1-9:/drawable/notification_bg_low.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-release-26:/drawable/notification_icon_background.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-core-1.13.1-9:/drawable/notification_icon_background.xml"}]