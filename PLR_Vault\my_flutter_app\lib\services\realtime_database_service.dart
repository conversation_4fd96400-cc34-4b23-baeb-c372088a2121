import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/models.dart';

class RealtimeDatabaseService {
  static final RealtimeDatabaseService _instance = RealtimeDatabaseService._internal();
  factory RealtimeDatabaseService() => _instance;
  RealtimeDatabaseService._internal();

  final FirebaseDatabase _database = FirebaseDatabase.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Database references
  DatabaseReference get _contentRef => _database.ref('content');
  DatabaseReference get _usersRef => _database.ref('users');

  /// Add content item to database
  Future<void> addContentItem(ContentItem item) async {
    try {
      await _contentRef.child(item.category).child(item.id).set(item.toMap());
    } catch (e) {
      throw Exception('Failed to add content item: $e');
    }
  }

  /// Add multiple content items to database
  Future<void> addMultipleContentItems(List<ContentItem> items) async {
    try {
      final Map<String, dynamic> updates = {};
      
      for (final item in items) {
        updates['content/${item.category}/${item.id}'] = item.toMap();
      }
      
      await _database.ref().update(updates);
    } catch (e) {
      throw Exception('Failed to add multiple content items: $e');
    }
  }

  /// Get all content items in a category
  Future<List<ContentItem>> getContentByCategory(String category) async {
    try {
      final DatabaseEvent event = await _contentRef.child(category).once();
      final Map<dynamic, dynamic>? data = event.snapshot.value as Map<dynamic, dynamic>?;
      
      if (data == null) return [];
      
      return data.entries.map((entry) {
        return ContentItem.fromMap(entry.key, entry.value);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get content by category: $e');
    }
  }

  /// Get all categories
  Future<List<String>> getCategories() async {
    try {
      final DatabaseEvent event = await _contentRef.once();
      final Map<dynamic, dynamic>? data = event.snapshot.value as Map<dynamic, dynamic>?;
      
      if (data == null) return [];
      
      return data.keys.cast<String>().toList();
    } catch (e) {
      throw Exception('Failed to get categories: $e');
    }
  }

  /// Get a specific content item
  Future<ContentItem?> getContentItem(String category, String itemId) async {
    try {
      final DatabaseEvent event = await _contentRef.child(category).child(itemId).once();
      final Map<dynamic, dynamic>? data = event.snapshot.value as Map<dynamic, dynamic>?;
      
      if (data == null) return null;
      
      return ContentItem.fromMap(itemId, data);
    } catch (e) {
      throw Exception('Failed to get content item: $e');
    }
  }

  /// Add download record for user
  Future<void> addUserDownload(UserDownload download) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');
      
      await _usersRef
          .child(userId)
          .child('downloads')
          .child(download.contentId)
          .set(download.toMap());
    } catch (e) {
      throw Exception('Failed to add user download: $e');
    }
  }

  /// Get all downloads for current user
  Future<List<UserDownload>> getUserDownloads() async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');
      
      final DatabaseEvent event = await _usersRef
          .child(userId)
          .child('downloads')
          .once();
      
      final Map<dynamic, dynamic>? data = event.snapshot.value as Map<dynamic, dynamic>?;
      
      if (data == null) return [];
      
      return data.entries.map((entry) {
        return UserDownload.fromMap(entry.key, entry.value);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get user downloads: $e');
    }
  }

  /// Check if user has downloaded a specific content item
  Future<bool> hasUserDownloaded(String contentId) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) return false;
      
      final DatabaseEvent event = await _usersRef
          .child(userId)
          .child('downloads')
          .child(contentId)
          .once();
      
      return event.snapshot.exists;
    } catch (e) {
      return false;
    }
  }

  /// Remove download record for user
  Future<void> removeUserDownload(String contentId) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');
      
      await _usersRef
          .child(userId)
          .child('downloads')
          .child(contentId)
          .remove();
    } catch (e) {
      throw Exception('Failed to remove user download: $e');
    }
  }

  /// Update download status
  Future<void> updateDownloadStatus(String contentId, bool isDownloaded) async {
    try {
      final String? userId = _auth.currentUser?.uid;
      if (userId == null) throw Exception('User not authenticated');
      
      await _usersRef
          .child(userId)
          .child('downloads')
          .child(contentId)
          .child('isDownloaded')
          .set(isDownloaded);
    } catch (e) {
      throw Exception('Failed to update download status: $e');
    }
  }

  /// Stream of user downloads (real-time updates)
  Stream<List<UserDownload>> getUserDownloadsStream() {
    final String? userId = _auth.currentUser?.uid;
    if (userId == null) return Stream.value([]);
    
    return _usersRef
        .child(userId)
        .child('downloads')
        .onValue
        .map((event) {
      final Map<dynamic, dynamic>? data = event.snapshot.value as Map<dynamic, dynamic>?;
      
      if (data == null) return <UserDownload>[];
      
      return data.entries.map((entry) {
        return UserDownload.fromMap(entry.key, entry.value);
      }).toList();
    });
  }

  /// Stream of content by category (real-time updates)
  Stream<List<ContentItem>> getContentByCategoryStream(String category) {
    return _contentRef.child(category).onValue.map((event) {
      final Map<dynamic, dynamic>? data = event.snapshot.value as Map<dynamic, dynamic>?;
      
      if (data == null) return <ContentItem>[];
      
      return data.entries.map((entry) {
        return ContentItem.fromMap(entry.key, entry.value);
      }).toList();
    });
  }
}
