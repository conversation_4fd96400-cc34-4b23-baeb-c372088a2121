import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:path/path.dart' as path;
import '../models/models.dart';
import '../services/firebase_storage_service.dart';
import '../services/realtime_database_service.dart';

class ContentUploader {
  final FirebaseStorageService _storageService = FirebaseStorageService();
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();

  /// Upload content from a local directory to Firebase
  Future<void> uploadContentFromDirectory(String directoryPath) async {
    try {
      print('Starting content upload from: $directoryPath');
      
      final Directory contentDir = Directory(directoryPath);
      if (!await contentDir.exists()) {
        throw Exception('Content directory does not exist: $directoryPath');
      }

      // Get all subdirectories (categories)
      final List<FileSystemEntity> entities = await contentDir.list().toList();
      final List<Directory> categoryDirs = entities
          .whereType<Directory>()
          .where((dir) => !path.basename(dir.path).startsWith('.'))
          .toList();

      print('Found ${categoryDirs.length} categories to upload');

      for (final Directory categoryDir in categoryDirs) {
        final String categoryName = path.basename(categoryDir.path);
        print('\nProcessing category: $categoryName');
        
        await _uploadCategoryContent(categoryDir, categoryName);
      }

      print('\nContent upload completed successfully!');
    } catch (e) {
      print('Error uploading content: $e');
      rethrow;
    }
  }

  /// Upload content for a specific category
  Future<void> _uploadCategoryContent(Directory categoryDir, String categoryName) async {
    try {
      // Get all files in the category directory
      final List<File> files = await _getFilesInDirectory(categoryDir);
      
      if (files.isEmpty) {
        print('No files found in category: $categoryName');
        return;
      }

      print('Found ${files.length} files in $categoryName');

      final List<ContentItem> contentItems = [];

      for (final File file in files) {
        try {
          print('Uploading: ${path.basename(file.path)}');
          
          // Upload file to Firebase Storage
          final String downloadUrl = await _storageService.uploadFile(
            file: file,
            category: categoryName,
            fileName: path.basename(file.path),
            onProgress: (progress) {
              print('  Progress: ${(progress * 100).toStringAsFixed(1)}%');
            },
          );

          // Create content item
          final ContentItem contentItem = _createContentItem(
            file: file,
            category: categoryName,
            downloadUrl: downloadUrl,
          );

          contentItems.add(contentItem);
          print('  Uploaded successfully: ${contentItem.title}');
        } catch (e) {
          print('  Failed to upload ${path.basename(file.path)}: $e');
          // Continue with other files
        }
      }

      // Add all content items to database
      if (contentItems.isNotEmpty) {
        await _dbService.addMultipleContentItems(contentItems);
        print('Added ${contentItems.length} items to database for category: $categoryName');
      }
    } catch (e) {
      print('Error uploading category $categoryName: $e');
      rethrow;
    }
  }

  /// Get all files in a directory recursively
  Future<List<File>> _getFilesInDirectory(Directory directory) async {
    final List<File> files = [];
    
    await for (final FileSystemEntity entity in directory.list(recursive: true)) {
      if (entity is File) {
        final String fileName = path.basename(entity.path);
        final String extension = path.extension(fileName).toLowerCase();
        
        // Only include supported file types
        if (_isSupportedFileType(extension)) {
          files.add(entity);
        }
      }
    }
    
    return files;
  }

  /// Check if file type is supported
  bool _isSupportedFileType(String extension) {
    const List<String> supportedTypes = ['.pdf', '.mp4', '.txt', '.doc', '.docx', '.mov', '.avi', '.mkv'];
    return supportedTypes.contains(extension);
  }

  /// Create a ContentItem from a file
  ContentItem _createContentItem({
    required File file,
    required String category,
    required String downloadUrl,
  }) {
    final String fileName = path.basename(file.path);
    final String fileNameWithoutExt = path.basenameWithoutExtension(file.path);
    final String extension = path.extension(file.path);
    final int fileSize = file.lengthSync();

    // Generate a unique ID for the content item
    final String id = _generateContentId(fileName, category);

    // Create a title from the filename
    final String title = _generateTitle(fileNameWithoutExt);

    // Generate a description based on the file type and category
    final String description = _generateDescription(title, category, extension);

    return ContentItem(
      id: id,
      title: title,
      description: description,
      category: category,
      fileUrl: downloadUrl,
      fileName: fileName,
      fileType: extension.replaceFirst('.', ''),
      fileSize: fileSize,
      createdAt: DateTime.now(),
    );
  }

  /// Generate a unique content ID
  String _generateContentId(String fileName, String category) {
    final String sanitized = fileName
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .replaceAll(RegExp(r'^_|_$'), '');
    
    return '${category.toLowerCase()}_${sanitized}_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Generate a title from filename
  String _generateTitle(String fileNameWithoutExt) {
    return fileNameWithoutExt
        .replaceAll(RegExp(r'[_-]'), ' ')
        .split(' ')
        .map((word) => word.isNotEmpty 
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : word)
        .join(' ')
        .trim();
  }

  /// Generate a description based on content
  String _generateDescription(String title, String category, String extension) {
    final String fileType = _getFileTypeDescription(extension);
    
    return 'A $fileType resource about $title in the $category category. '
           'This PLR/MRR content is ready for download and use.';
  }

  /// Get file type description
  String _getFileTypeDescription(String extension) {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return 'PDF document';
      case '.mp4':
      case '.mov':
      case '.avi':
      case '.mkv':
        return 'video';
      case '.txt':
        return 'text document';
      case '.doc':
      case '.docx':
        return 'Word document';
      default:
        return 'file';
    }
  }

  /// Upload sample content for testing
  Future<void> uploadSampleContent() async {
    try {
      print('Creating sample content for testing...');

      final List<ContentItem> sampleItems = [
        ContentItem(
          id: 'business_affiliate_marketing_guide',
          title: 'Affiliate Marketing Guide',
          description: 'Complete guide to affiliate marketing strategies and techniques',
          category: 'Business',
          fileUrl: 'https://example.com/sample.pdf', // This would be a real URL after upload
          fileName: 'affiliate_marketing_guide.pdf',
          fileType: 'pdf',
          fileSize: 1024000, // 1MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'health_nutrition_basics',
          title: 'Nutrition Basics',
          description: 'Essential nutrition information for healthy living',
          category: 'Health',
          fileUrl: 'https://example.com/sample2.pdf',
          fileName: 'nutrition_basics.pdf',
          fileType: 'pdf',
          fileSize: 2048000, // 2MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'tech_ai_introduction',
          title: 'Introduction to AI',
          description: 'Basic concepts and applications of artificial intelligence',
          category: 'Technology',
          fileUrl: 'https://example.com/sample3.txt',
          fileName: 'ai_introduction.txt',
          fileType: 'txt',
          fileSize: 512000, // 512KB
          createdAt: DateTime.now(),
        ),
      ];

      await _dbService.addMultipleContentItems(sampleItems);
      print('Sample content added successfully!');
    } catch (e) {
      print('Error adding sample content: $e');
      rethrow;
    }
  }
}
