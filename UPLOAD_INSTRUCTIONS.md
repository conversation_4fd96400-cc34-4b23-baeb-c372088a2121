# 🚀 Firebase PLR Content Upload Instructions

This script will upload all your PLR content from the `content` folder to Firebase Storage and add metadata to Realtime Database.

## 📋 Prerequisites

1. **Node.js** installed on your system
2. **Firebase CLI** installed: `npm install -g firebase-tools`
3. **Content folder** exists in the project root

## 🔧 Setup & Upload

### Option 1: Automatic Setup (Recommended)

**Windows:**
```bash
setup_and_upload.bat
```

**Linux/Mac:**
```bash
./setup_and_upload.sh
```

### Option 2: Manual Setup

1. **Install dependencies:**
   ```bash
   npm install firebase-admin
   ```

2. **Login to Firebase CLI:**
   ```bash
   firebase login
   ```

3. **Run the upload script:**
   ```bash
   node firebase_uploader.js
   ```

## 📁 Content Structure

The script will process these folders and map them to categories:

- `AIForProductivity` → **Technology**
- `AffCommissionFormula` → **Business**
- `AffiliateCashMastery` → **Business**
- `12,000 Recipes bonus-...` → **Health**
- Other folders → **General**

## 📊 Upload Limits

- **Max files per category:** 10 files
- **Max total files:** 50 files
- **Supported formats:** PDF, MP4, TXT, DOC, DOCX, MOV, AVI, MKV

## 🎯 What the Script Does

1. **Scans content folder** recursively for supported files
2. **Uploads files** to Firebase Storage at `gs://myapp-59f81.firebasestorage.app/content/{category}/`
3. **Makes files public** for easy access
4. **Creates database entries** in Realtime Database with metadata:
   - Title (generated from filename)
   - Description
   - Category
   - File URL
   - File size
   - Upload timestamp

## 📱 Firebase Console

After upload, check:

1. **Storage:** https://console.firebase.google.com/project/myapp-59f81/storage
2. **Database:** https://console.firebase.google.com/project/myapp-59f81/database

## 🔍 Troubleshooting

**"Permission denied" errors:**
- Make sure you're logged in: `firebase login`
- Check Firebase project permissions

**"Content directory not found":**
- Ensure the `content` folder exists in the project root
- Check folder permissions

**"Module not found" errors:**
- Run: `npm install firebase-admin`

## 📝 Output

The script will show:
- ✅ Successful uploads
- ❌ Failed uploads with error messages
- 📊 Upload statistics

## 🎉 After Upload

Once upload is complete:
1. Your Flutter app will automatically show the uploaded content
2. Users can browse categories and download files
3. All files are stored in Firebase Storage with public URLs

## 🔒 Security Note

Files are made publicly readable for the app to work. For production, consider implementing proper Firebase Security Rules based on user authentication.
