[{"merged": "com.crazecoder.openfile.open_filex-release-29:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml", "source": "com.crazecoder.openfile.open_filex-appcompat-1.6.1-11:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml", "source": "com.crazecoder.openfile.open_filex-appcompat-1.6.1-11:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml", "source": "com.crazecoder.openfile.open_filex-appcompat-1.6.1-11:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml", "source": "com.crazecoder.openfile.open_filex-appcompat-1.6.1-11:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml", "source": "com.crazecoder.openfile.open_filex-appcompat-1.6.1-11:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml", "source": "com.crazecoder.openfile.open_filex-appcompat-1.6.1-11:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/interpolator/fast_out_slow_in.xml", "source": "com.crazecoder.openfile.open_filex-appcompat-1.6.1-11:/interpolator/fast_out_slow_in.xml"}]