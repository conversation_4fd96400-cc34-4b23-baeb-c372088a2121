com.plrvault.app-browser-1.8.0-0 C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\res
com.plrvault.app-jetified-lifecycle-process-2.7.0-1 C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\res
com.plrvault.app-jetified-appcompat-resources-1.6.1-2 C:\Users\<USER>\.gradle\caches\8.12\transforms\0f448994e490bf59f20aa19226509e50\transformed\jetified-appcompat-resources-1.6.1\res
com.plrvault.app-jetified-play-services-basement-18.4.0-3 C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\res
com.plrvault.app-jetified-window-1.2.0-4 C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\res
com.plrvault.app-fragment-1.7.1-5 C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\res
com.plrvault.app-core-runtime-2.2.0-6 C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\res
com.plrvault.app-lifecycle-livedata-core-2.7.0-7 C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\res
com.plrvault.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-8 C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.plrvault.app-jetified-tracing-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\res
com.plrvault.app-jetified-emoji2-1.2.0-10 C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\res
com.plrvault.app-jetified-core-1.0.0-11 C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\res
com.plrvault.app-jetified-firebase-common-20.4.3-12 C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\res
com.plrvault.app-jetified-ads-adservices-java-1.0.0-beta05-13 C:\Users\<USER>\.gradle\caches\8.12\transforms\49e80b404c42bd03696302491626128a\transformed\jetified-ads-adservices-java-1.0.0-beta05\res
com.plrvault.app-appcompat-1.6.1-14 C:\Users\<USER>\.gradle\caches\8.12\transforms\4a06d06ec0ce1c73cc8df2ae11f0f21f\transformed\appcompat-1.6.1\res
com.plrvault.app-jetified-window-java-1.2.0-15 C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\res
com.plrvault.app-core-1.13.1-16 C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\res
com.plrvault.app-jetified-annotation-experimental-1.4.1-17 C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\res
com.plrvault.app-webkit-1.12.1-18 C:\Users\<USER>\.gradle\caches\8.12\transforms\6d1284f70efaea5e2f7428ac2d9ae231\transformed\webkit-1.12.1\res
com.plrvault.app-jetified-play-services-base-18.1.0-19 C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\res
com.plrvault.app-jetified-lifecycle-service-2.7.0-20 C:\Users\<USER>\.gradle\caches\8.12\transforms\7d813ca98b23cd30a6261e035c9b7933\transformed\jetified-lifecycle-service-2.7.0\res
com.plrvault.app-jetified-savedstate-1.2.1-21 C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\res
com.plrvault.app-lifecycle-viewmodel-2.7.0-22 C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\res
com.plrvault.app-jetified-startup-runtime-1.1.1-23 C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\res
com.plrvault.app-work-runtime-2.7.0-24 C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\res
com.plrvault.app-jetified-profileinstaller-1.3.1-25 C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\res
com.plrvault.app-lifecycle-runtime-2.7.0-26 C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\res
com.plrvault.app-jetified-emoji2-views-helper-1.2.0-27 C:\Users\<USER>\.gradle\caches\8.12\transforms\c2bb4cddb87481b6b754c0fdd3839a9b\transformed\jetified-emoji2-views-helper-1.2.0\res
com.plrvault.app-jetified-play-services-auth-21.0.0-28 C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\res
com.plrvault.app-jetified-lifecycle-livedata-core-ktx-2.7.0-29 C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.plrvault.app-jetified-play-services-ads-23.6.0-30 C:\Users\<USER>\.gradle\caches\8.12\transforms\e5aa58ad971ef0b034990717d52ef9e6\transformed\jetified-play-services-ads-23.6.0\res
com.plrvault.app-jetified-activity-1.8.1-31 C:\Users\<USER>\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\res
com.plrvault.app-lifecycle-livedata-2.7.0-32 C:\Users\<USER>\.gradle\caches\8.12\transforms\e72f610bb8a20735f78a04c908b9b793\transformed\lifecycle-livedata-2.7.0\res
com.plrvault.app-jetified-core-ktx-1.13.1-33 C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\res
com.plrvault.app-jetified-ads-adservices-1.0.0-beta05-34 C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\res
com.plrvault.app-constraintlayout-2.1.4-35 C:\Users\<USER>\.gradle\caches\8.12\transforms\f43a8a95ab2f768e2215dacb56c18080\transformed\constraintlayout-2.1.4\res
com.plrvault.app-jetified-play-services-ads-lite-23.6.0-36 C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\res
com.plrvault.app-main-37 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\res
com.plrvault.app-release-38 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\release\res
com.plrvault.app-pngs-39 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\app\generated\res\pngs\release
com.plrvault.app-res-40 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\app\generated\res\processReleaseGoogleServices
com.plrvault.app-resValues-41 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\app\generated\res\resValues\release
com.plrvault.app-packageReleaseResources-42 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\app\intermediates\incremental\release\packageReleaseResources\merged.dir
com.plrvault.app-packageReleaseResources-43 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\app\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.plrvault.app-release-44 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\app\intermediates\merged_res\release\mergeReleaseResources
com.plrvault.app-release-45 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\device_info_plus\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-46 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-47 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_core\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-48 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_database\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-49 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_storage\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-50 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\google_mobile_ads\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-51 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\google_sign_in_android\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-52 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-53 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\path_provider_android\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-54 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\permission_handler_android\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-55 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\sqflite_android\intermediates\packaged_res\release\packageReleaseResources
com.plrvault.app-release-56 E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\webview_flutter_android\intermediates\packaged_res\release\packageReleaseResources
