#!/bin/bash

echo "========================================"
echo "Firebase PLR Content Uploader"
echo "========================================"
echo

echo "1. Installing dependencies..."
npm install firebase-admin
if [ $? -ne 0 ]; then
    echo "Failed to install dependencies"
    exit 1
fi

echo
echo "2. Checking Firebase CLI login..."
firebase projects:list > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "You need to login to Firebase CLI first"
    echo "Running: firebase login"
    firebase login
fi

echo
echo "3. Starting content upload..."
node firebase_uploader.js

echo
echo "Upload completed!"
