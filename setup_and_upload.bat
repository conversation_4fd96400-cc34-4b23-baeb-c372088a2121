@echo off
echo ========================================
echo Firebase PLR Content Uploader
echo ========================================
echo.

echo 1. Installing dependencies...
npm install firebase-admin
if %errorlevel% neq 0 (
    echo Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo 2. Checking Firebase CLI login...
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo You need to login to Firebase CLI first
    echo Running: firebase login
    firebase login
)

echo.
echo 3. Starting content upload...
node firebase_uploader.js

echo.
echo Upload completed!
pause
