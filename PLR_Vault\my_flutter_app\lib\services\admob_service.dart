import 'dart:async';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:flutter/foundation.dart';

class AdMobService {
  static final AdMobService _instance = AdMobService._internal();
  factory AdMobService() => _instance;
  AdMobService._internal();

  RewardedAd? _rewardedAd;
  bool _isRewardedAdReady = false;
  bool _isLoading = false;

  // Ad Unit IDs
  static const String _testRewardedAdUnitId =
      'ca-app-pub-3940256099942544/5224354917';
  static const String _productionRewardedAdUnitId =
      'ca-app-pub-3815633128309017/7396319807';

  // Use test ads during development, production ads in release
  String get _rewardedAdUnitId {
    // TEMPORARY: Force production ads for testing
    // TODO: Remove this override before final release
    // return _productionRewardedAdUnitId;

    if (kDebugMode) {
      return _testRewardedAdUnitId;
    }
    return _productionRewardedAdUnitId;
  }

  /// Initialize AdMob
  static Future<void> initialize() async {
    await MobileAds.instance.initialize();
    if (kDebugMode) {
      print('AdMob initialized successfully');
    }
  }

  /// Load a rewarded ad
  Future<void> loadRewardedAd() async {
    if (_isLoading || _isRewardedAdReady) return;

    _isLoading = true;

    await RewardedAd.load(
      adUnitId: _rewardedAdUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          _rewardedAd = ad;
          _isRewardedAdReady = true;
          _isLoading = false;
          if (kDebugMode) {
            print('Rewarded ad loaded successfully');
          }
        },
        onAdFailedToLoad: (LoadAdError error) {
          _isRewardedAdReady = false;
          _isLoading = false;
          if (kDebugMode) {
            print('Failed to load rewarded ad: ${error.message}');
          }
        },
      ),
    );
  }

  /// Show rewarded ad
  Future<bool> showRewardedAd() async {
    if (!_isRewardedAdReady || _rewardedAd == null) {
      if (kDebugMode) {
        print('Rewarded ad not ready');
      }
      return false;
    }

    final Completer<bool> completer = Completer<bool>();
    bool rewardEarned = false;

    _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (RewardedAd ad) {
        if (kDebugMode) {
          print('Rewarded ad showed full screen content');
        }
      },
      onAdDismissedFullScreenContent: (RewardedAd ad) {
        if (kDebugMode) {
          print('Rewarded ad dismissed. Reward earned: $rewardEarned');
        }
        ad.dispose();
        _rewardedAd = null;
        _isRewardedAdReady = false;

        // Complete the future with the reward status
        if (!completer.isCompleted) {
          completer.complete(rewardEarned);
        }

        // Preload next ad
        loadRewardedAd();
      },
      onAdFailedToShowFullScreenContent: (RewardedAd ad, AdError error) {
        if (kDebugMode) {
          print('Failed to show rewarded ad: ${error.message}');
        }
        ad.dispose();
        _rewardedAd = null;
        _isRewardedAdReady = false;

        // Complete the future with failure
        if (!completer.isCompleted) {
          completer.complete(false);
        }

        // Preload next ad
        loadRewardedAd();
      },
    );

    try {
      await _rewardedAd!.show(
        onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
          rewardEarned = true;
          if (kDebugMode) {
            print('User earned reward: ${reward.amount} ${reward.type}');
          }
        },
      );

      // Wait for the ad to be dismissed and return the reward status
      // Add a timeout to prevent hanging indefinitely
      return await completer.future.timeout(
        const Duration(minutes: 2),
        onTimeout: () {
          if (kDebugMode) {
            print('Ad completion timed out');
          }
          return false;
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error showing rewarded ad: $e');
      }
      if (!completer.isCompleted) {
        completer.complete(false);
      }
      return false;
    }
  }

  /// Check if rewarded ad is ready to show
  bool get isRewardedAdReady => _isRewardedAdReady;

  /// Check if ad is currently loading
  bool get isLoading => _isLoading;

  /// Dispose of the current ad
  void dispose() {
    _rewardedAd?.dispose();
    _rewardedAd = null;
    _isRewardedAdReady = false;
    _isLoading = false;
  }

  /// Preload ads for better user experience
  Future<void> preloadAds() async {
    if (!_isRewardedAdReady && !_isLoading) {
      await loadRewardedAd();
    }
  }
}
