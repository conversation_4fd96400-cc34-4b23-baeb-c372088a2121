[{"merged": "com.crazecoder.openfile.open_filex-release-29:/animator/fragment_open_enter.xml", "source": "com.crazecoder.openfile.open_filex-fragment-1.7.1-3:/animator/fragment_open_enter.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/animator/fragment_close_enter.xml", "source": "com.crazecoder.openfile.open_filex-fragment-1.7.1-3:/animator/fragment_close_enter.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/animator/fragment_open_exit.xml", "source": "com.crazecoder.openfile.open_filex-fragment-1.7.1-3:/animator/fragment_open_exit.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/animator/fragment_fade_exit.xml", "source": "com.crazecoder.openfile.open_filex-fragment-1.7.1-3:/animator/fragment_fade_exit.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/animator/fragment_close_exit.xml", "source": "com.crazecoder.openfile.open_filex-fragment-1.7.1-3:/animator/fragment_close_exit.xml"}, {"merged": "com.crazecoder.openfile.open_filex-release-29:/animator/fragment_fade_enter.xml", "source": "com.crazecoder.openfile.open_filex-fragment-1.7.1-3:/animator/fragment_fade_enter.xml"}]