[{"merged": "com.example.my_flutter_app-debug-44:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-38:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-44:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-38:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-44:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-38:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-44:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-38:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-44:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-38:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-44:/drawable-v21_launch_background.xml.flat", "source": "com.example.my_flutter_app-main-38:/drawable-v21/launch_background.xml"}]