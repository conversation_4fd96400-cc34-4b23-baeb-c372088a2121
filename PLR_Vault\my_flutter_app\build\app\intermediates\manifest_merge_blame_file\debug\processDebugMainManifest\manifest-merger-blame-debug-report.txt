1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.my_flutter_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:3:5-67
15-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission
16-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:4:5-5:38
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:4:22-78
18        android:maxSdkVersion="32" />
18-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:5:9-35
19    <uses-permission
19-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:6:5-7:38
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:6:22-77
21        android:maxSdkVersion="32" />
21-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:7:9-35
22    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- Android 13+ Media permissions -->
22-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:8:5-82
22-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:8:22-79
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:11:5-76
23-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:11:22-73
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:12:5-75
24-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:12:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
25-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:13:5-75
25-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:13:22-72
26    <!--
27 Required to query activities that can process text, see:
28         https://developer.android.com/training/package-visibility and
29         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
30
31         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
32    -->
33    <queries>
33-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:57:5-62:15
34        <intent>
34-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:58:9-61:18
35            <action android:name="android.intent.action.PROCESS_TEXT" />
35-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:59:13-72
35-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:59:21-70
36
37            <data android:mimeType="text/plain" />
37-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
37-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:19-48
38        </intent>
39        <!-- For browser content -->
40        <intent>
40-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:38:9-44:18
41            <action android:name="android.intent.action.VIEW" />
41-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
41-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
42
43            <category android:name="android.intent.category.BROWSABLE" />
43-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
43-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
44
45            <data android:scheme="https" />
45-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
46        </intent> <!-- End of browser content -->
47        <!-- For CustomTabsService -->
48        <intent>
48-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:47:9-49:18
49            <action android:name="android.support.customtabs.action.CustomTabsService" />
49-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:48:13-90
49-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:48:21-87
50        </intent> <!-- End of CustomTabsService -->
51        <!-- For MRAID capabilities -->
52        <intent>
52-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:52:9-56:18
53            <action android:name="android.intent.action.INSERT" />
53-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:53:13-67
53-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:53:21-64
54
55            <data android:mimeType="vnd.android.cursor.dir/event" />
55-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
55-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:19-48
56        </intent>
57        <intent>
57-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:57:9-61:18
58            <action android:name="android.intent.action.VIEW" />
58-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
58-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
59
60            <data android:scheme="sms" />
60-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
61        </intent>
62        <intent>
62-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:62:9-66:18
63            <action android:name="android.intent.action.DIAL" />
63-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:63:13-65
63-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:63:21-62
64
65            <data android:path="tel:" />
65-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
66        </intent>
67    </queries>
68
69    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
69-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
69-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
70    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
70-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:26:5-79
70-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:26:22-76
71    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
71-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:27:5-82
71-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:27:22-79
72    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
72-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:28:5-88
72-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:28:22-85
73    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
73-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:29:5-83
73-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:29:22-80
74    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
74-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
74-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
75    <uses-permission android:name="android.permission.WAKE_LOCK" />
75-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
75-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:22-65
76    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
76-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
76-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
77
78    <permission
78-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
79        android:name="com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
79-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
80        android:protectionLevel="signature" />
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
81
82    <uses-permission android:name="com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
83
84    <application
85        android:name="android.app.Application"
86        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
86-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
87        android:debuggable="true"
88        android:extractNativeLibs="true"
89        android:icon="@mipmap/ic_launcher"
90        android:label="PLR Content App" >
91        <activity
92            android:name="com.example.my_flutter_app.MainActivity"
93            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
94            android:exported="true"
95            android:hardwareAccelerated="true"
96            android:launchMode="singleTop"
97            android:taskAffinity=""
98            android:theme="@style/LaunchTheme"
99            android:windowSoftInputMode="adjustResize" >
100
101            <!--
102                 Specifies an Android theme to apply to this Activity as soon as
103                 the Android process has started. This theme is visible to the user
104                 while the Flutter UI initializes. After that, this theme continues
105                 to determine the Window background behind the Flutter UI.
106            -->
107            <meta-data
108                android:name="io.flutter.embedding.android.NormalTheme"
109                android:resource="@style/NormalTheme" />
110
111            <intent-filter>
112                <action android:name="android.intent.action.MAIN" />
113
114                <category android:name="android.intent.category.LAUNCHER" />
115            </intent-filter>
116        </activity>
117        <!--
118             Don't delete the meta-data below.
119             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
120        -->
121        <meta-data
122            android:name="flutterEmbedding"
123            android:value="2" />
124
125        <!-- AdMob App ID -->
126        <meta-data
127            android:name="com.google.android.gms.ads.APPLICATION_ID"
128            android:value="ca-app-pub-3815633128309017~1217924262" />
129        <meta-data
129-->[:google_mobile_ads] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-12:36
130            android:name="io.flutter.embedded_views_preview"
130-->[:google_mobile_ads] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-61
131            android:value="true" />
131-->[:google_mobile_ads] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-33
132
133        <provider
133-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:9-25:20
134            android:name="com.crazecoder.openfile.FileProvider"
134-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-64
135            android:authorities="com.example.my_flutter_app.fileProvider.com.crazecoder.openfile"
135-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-88
136            android:exported="false"
136-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-37
137            android:grantUriPermissions="true" >
137-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-47
138            <meta-data
138-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-24:53
139                android:name="android.support.FILE_PROVIDER_PATHS"
139-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-67
140                android:resource="@xml/filepaths" />
140-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-50
141        </provider>
142
143        <service
143-->[:firebase_auth] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
144            android:name="com.google.firebase.components.ComponentDiscoveryService"
144-->[:firebase_auth] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
145            android:directBootAware="true"
145-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
146            android:exported="false" >
146-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
147            <meta-data
147-->[:firebase_auth] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
148                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
148-->[:firebase_auth] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
149                android:value="com.google.firebase.components.ComponentRegistrar" />
149-->[:firebase_auth] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
150            <meta-data
150-->[:firebase_database] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
151                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
151-->[:firebase_database] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-127
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[:firebase_database] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
153            <meta-data
153-->[:firebase_storage] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
154                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
154-->[:firebase_storage] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
155                android:value="com.google.firebase.components.ComponentRegistrar" />
155-->[:firebase_storage] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
156            <meta-data
156-->[:firebase_core] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
157                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
157-->[:firebase_core] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
158                android:value="com.google.firebase.components.ComponentRegistrar" />
158-->[:firebase_core] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
159            <meta-data
159-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
160                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
160-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
161                android:value="com.google.firebase.components.ComponentRegistrar" />
161-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
162            <meta-data
162-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
163                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
163-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
164                android:value="com.google.firebase.components.ComponentRegistrar" />
164-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
165            <meta-data
165-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
166                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
166-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
167                android:value="com.google.firebase.components.ComponentRegistrar" />
167-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
168            <meta-data
168-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:29:13-31:85
169                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
169-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:30:17-120
170                android:value="com.google.firebase.components.ComponentRegistrar" />
170-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:31:17-82
171            <meta-data
171-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:32:13-34:85
172                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
172-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:33:17-109
173                android:value="com.google.firebase.components.ComponentRegistrar" />
173-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:34:17-82
174            <meta-data
174-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
175                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
175-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
176                android:value="com.google.firebase.components.ComponentRegistrar" />
176-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
177            <meta-data
177-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
178                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
178-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
179                android:value="com.google.firebase.components.ComponentRegistrar" />
179-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
180            <meta-data
180-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
181                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
181-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
182                android:value="com.google.firebase.components.ComponentRegistrar" />
182-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
183            <meta-data
183-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
184                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
184-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
185                android:value="com.google.firebase.components.ComponentRegistrar" />
185-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
186        </service>
187
188        <activity
188-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
189            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
189-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
190            android:excludeFromRecents="true"
190-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
191            android:exported="false"
191-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
192            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
192-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
193        <!--
194            Service handling Google Sign-In user revocation. For apps that do not integrate with
195            Google Sign-In, this service will never be started.
196        -->
197        <service
197-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
198            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
198-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
199            android:exported="true"
199-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
200            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
200-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
201            android:visibleToInstantApps="true" />
201-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
202
203        <activity
203-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
204            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
204-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
205            android:excludeFromRecents="true"
205-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
206            android:exported="true"
206-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
207            android:launchMode="singleTask"
207-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
208            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
208-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
209            <intent-filter>
209-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
210                <action android:name="android.intent.action.VIEW" />
210-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
210-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
211
212                <category android:name="android.intent.category.DEFAULT" />
212-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
212-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
213                <category android:name="android.intent.category.BROWSABLE" />
213-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
213-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
214
215                <data
215-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
216                    android:host="firebase.auth"
217                    android:path="/"
218                    android:scheme="genericidp" />
219            </intent-filter>
220        </activity>
221        <activity
221-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
222            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
222-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
223            android:excludeFromRecents="true"
223-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
224            android:exported="true"
224-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
225            android:launchMode="singleTask"
225-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
226            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
226-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
227            <intent-filter>
227-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
228                <action android:name="android.intent.action.VIEW" />
228-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
228-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
229
230                <category android:name="android.intent.category.DEFAULT" />
230-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
230-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
231                <category android:name="android.intent.category.BROWSABLE" />
231-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
231-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
232
233                <data
233-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
234                    android:host="firebase.auth"
235                    android:path="/"
236                    android:scheme="recaptcha" />
237            </intent-filter>
238        </activity> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
239        <activity
239-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:73:9-78:43
240            android:name="com.google.android.gms.ads.AdActivity"
240-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:74:13-65
241            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
241-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:75:13-122
242            android:exported="false"
242-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:76:13-37
243            android:theme="@android:style/Theme.Translucent" />
243-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:77:13-61
244
245        <provider
245-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:80:9-85:43
246            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
246-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:81:13-76
247            android:authorities="com.example.my_flutter_app.mobileadsinitprovider"
247-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:82:13-73
248            android:exported="false"
248-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:83:13-37
249            android:initOrder="100" />
249-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:84:13-36
250
251        <service
251-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:87:9-91:43
252            android:name="com.google.android.gms.ads.AdService"
252-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:88:13-64
253            android:enabled="true"
253-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:89:13-35
254            android:exported="false" />
254-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:90:13-37
255
256        <activity
256-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:93:9-97:43
257            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
257-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:94:13-82
258            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
258-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:95:13-122
259            android:exported="false" />
259-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:96:13-37
260        <activity
260-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:98:9-105:43
261            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
261-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:99:13-82
262            android:excludeFromRecents="true"
262-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:100:13-46
263            android:exported="false"
263-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:101:13-37
264            android:launchMode="singleTask"
264-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:102:13-44
265            android:taskAffinity=""
265-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:103:13-36
266            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
266-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:104:13-72
267
268        <property
268-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:107:9-109:62
269            android:name="android.adservices.AD_SERVICES_CONFIG"
269-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:108:13-65
270            android:resource="@xml/gma_ad_services_config" />
270-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:109:13-59
271
272        <provider
272-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
273            android:name="com.google.firebase.provider.FirebaseInitProvider"
273-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
274            android:authorities="com.example.my_flutter_app.firebaseinitprovider"
274-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
275            android:directBootAware="true"
275-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
276            android:exported="false"
276-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
277            android:initOrder="100" />
277-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
278
279        <activity
279-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
280            android:name="com.google.android.gms.common.api.GoogleApiActivity"
280-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
281            android:exported="false"
281-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
282            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
282-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
283
284        <provider
284-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
285            android:name="androidx.startup.InitializationProvider"
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
286            android:authorities="com.example.my_flutter_app.androidx-startup"
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
287            android:exported="false" >
287-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
288            <meta-data
288-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
289                android:name="androidx.work.WorkManagerInitializer"
289-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
290                android:value="androidx.startup" />
290-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
291            <meta-data
291-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
292                android:name="androidx.emoji2.text.EmojiCompatInitializer"
292-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
293                android:value="androidx.startup" />
293-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
294            <meta-data
294-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
295                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
295-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
296                android:value="androidx.startup" />
296-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
297            <meta-data
297-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
298                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
298-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
299                android:value="androidx.startup" />
299-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
300        </provider>
301
302        <service
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
303            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
304            android:directBootAware="false"
304-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
305            android:enabled="@bool/enable_system_alarm_service_default"
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
306            android:exported="false" />
306-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
307        <service
307-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
308            android:name="androidx.work.impl.background.systemjob.SystemJobService"
308-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
309            android:directBootAware="false"
309-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
310            android:enabled="@bool/enable_system_job_service_default"
310-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
311            android:exported="true"
311-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
312            android:permission="android.permission.BIND_JOB_SERVICE" />
312-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
313        <service
313-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
314            android:name="androidx.work.impl.foreground.SystemForegroundService"
314-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
315            android:directBootAware="false"
315-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
316            android:enabled="@bool/enable_system_foreground_service_default"
316-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
317            android:exported="false" />
317-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
318
319        <receiver
319-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
320            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
320-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
321            android:directBootAware="false"
321-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
322            android:enabled="true"
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
323            android:exported="false" />
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
324        <receiver
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
325            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
326            android:directBootAware="false"
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
327            android:enabled="false"
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
328            android:exported="false" >
328-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
329            <intent-filter>
329-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
330                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
331                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
332            </intent-filter>
333        </receiver>
334        <receiver
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
335            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
336            android:directBootAware="false"
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
337            android:enabled="false"
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
338            android:exported="false" >
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
339            <intent-filter>
339-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
340                <action android:name="android.intent.action.BATTERY_OKAY" />
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
341                <action android:name="android.intent.action.BATTERY_LOW" />
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
342            </intent-filter>
343        </receiver>
344        <receiver
344-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
345            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
345-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
346            android:directBootAware="false"
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
347            android:enabled="false"
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
348            android:exported="false" >
348-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
349            <intent-filter>
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
350                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
351                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
352            </intent-filter>
353        </receiver>
354        <receiver
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
355            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
356            android:directBootAware="false"
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
357            android:enabled="false"
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
358            android:exported="false" >
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
359            <intent-filter>
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
360                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
361            </intent-filter>
362        </receiver>
363        <receiver
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
364            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
365            android:directBootAware="false"
365-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
366            android:enabled="false"
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
367            android:exported="false" >
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
368            <intent-filter>
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
369                <action android:name="android.intent.action.BOOT_COMPLETED" />
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
370                <action android:name="android.intent.action.TIME_SET" />
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
371                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
372            </intent-filter>
373        </receiver>
374        <receiver
374-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
375            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
376            android:directBootAware="false"
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
377            android:enabled="@bool/enable_system_alarm_service_default"
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
378            android:exported="false" >
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
379            <intent-filter>
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
380                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
381            </intent-filter>
382        </receiver>
383        <receiver
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
384            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
385            android:directBootAware="false"
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
386            android:enabled="true"
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
387            android:exported="true"
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
388            android:permission="android.permission.DUMP" >
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
389            <intent-filter>
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
390                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
391            </intent-filter>
392        </receiver>
393
394        <uses-library
394-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
395            android:name="androidx.window.extensions"
395-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
396            android:required="false" />
396-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
397        <uses-library
397-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
398            android:name="androidx.window.sidecar"
398-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
399            android:required="false" />
399-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
400        <uses-library
400-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
401            android:name="android.ext.adservices"
401-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
402            android:required="false" />
402-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
403
404        <meta-data
404-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
405            android:name="com.google.android.gms.version"
405-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
406            android:value="@integer/google_play_services_version" />
406-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
407
408        <receiver
408-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
409            android:name="androidx.profileinstaller.ProfileInstallReceiver"
409-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
410            android:directBootAware="false"
410-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
411            android:enabled="true"
411-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
412            android:exported="true"
412-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
413            android:permission="android.permission.DUMP" >
413-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
414            <intent-filter>
414-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
415                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
415-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
415-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
416            </intent-filter>
417            <intent-filter>
417-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
418                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
418-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
418-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
419            </intent-filter>
420            <intent-filter>
420-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
421                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
422            </intent-filter>
423            <intent-filter>
423-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
424                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
425            </intent-filter>
426        </receiver>
427
428        <service
428-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
429            android:name="androidx.room.MultiInstanceInvalidationService"
429-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
430            android:directBootAware="true"
430-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
431            android:exported="false" />
431-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
432    </application>
433
434</manifest>
