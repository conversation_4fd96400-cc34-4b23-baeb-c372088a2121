// Node.js script to upload content to Firebase
// Run: npm install && npm run upload

const admin = require("firebase-admin");
const fs = require("fs");
const path = require("path");

// Initialize Firebase Admin SDK
// Using Application Default Credentials (Firebase CLI login)
try {
  admin.initializeApp({
    projectId: "myapp-59f81",
    storageBucket: "myapp-59f81.firebasestorage.app",
    databaseURL: "https://myapp-59f81-default-rtdb.firebaseio.com",
  });
  console.log("✅ Firebase Admin initialized successfully");
} catch (error) {
  console.error("❌ Firebase initialization failed:", error.message);
  console.log("Please run: firebase login");
  process.exit(1);
}

const bucket = admin.storage().bucket();
const db = admin.database();

async function uploadContent() {
  console.log("🚀 Starting content upload...");

  const contentDir = "./content";

  // Check if content directory exists
  if (!fs.existsSync(contentDir)) {
    console.error("❌ Content directory not found:", contentDir);
    console.log("Please ensure the content folder exists in the project root");
    return;
  }

  const categoryMapping = {
    AIForProductivity: "Technology",
    AffCommissionFormula: "Business",
    AffiliateCashMastery: "Business",
    "12,000 Recipes bonus-20250715T102342Z-1-001": "Health",
  };

  let totalUploaded = 0;
  const maxFiles = 50; // Limit total files for demo

  try {
    const folders = fs.readdirSync(contentDir);
    console.log(`📂 Found ${folders.length} folders in content directory`);

    for (const folder of folders) {
      if (totalUploaded >= maxFiles) {
        console.log(`⚠️ Reached maximum file limit (${maxFiles})`);
        break;
      }

      const folderPath = path.join(contentDir, folder);
      if (fs.statSync(folderPath).isDirectory()) {
        const category = categoryMapping[folder] || "General";
        console.log(`\n📁 Processing: ${folder} -> ${category}`);

        const uploaded = await uploadFolder(
          folderPath,
          category,
          maxFiles - totalUploaded
        );
        totalUploaded += uploaded;
      }
    }

    console.log(
      `\n🎉 Upload completed! Total files uploaded: ${totalUploaded}`
    );
  } catch (error) {
    console.error("❌ Upload failed:", error);
  }
}

async function uploadFolder(folderPath, category) {
  const files = getAllFiles(folderPath);
  let uploadCount = 0;

  for (const filePath of files) {
    if (uploadCount >= 5) break; // Limit for demo

    const fileName = path.basename(filePath);
    const extension = path.extname(fileName).toLowerCase();

    // Skip unsupported files
    if (![".pdf", ".mp4", ".txt", ".doc", ".docx"].includes(extension)) {
      continue;
    }

    try {
      console.log(`  📄 Uploading: ${fileName}`);

      // Upload to Storage
      const destination = `content/${category}/${fileName}`;
      await bucket.upload(filePath, { destination });

      // Get download URL
      const file = bucket.file(destination);
      const [url] = await file.getSignedUrl({
        action: "read",
        expires: "03-09-2491",
      });

      // Add to Realtime Database
      const contentItem = {
        title: generateTitle(path.parse(fileName).name),
        description: `A ${extension.slice(
          1
        )} resource in the ${category} category.`,
        category: category,
        fileUrl: url,
        fileName: fileName,
        fileType: extension.slice(1),
        fileSize: fs.statSync(filePath).size,
        createdAt: new Date().toISOString(),
      };

      const contentId = generateId(fileName, category);
      await db.ref(`content/${category}/${contentId}`).set(contentItem);

      uploadCount++;
      console.log(`    ✅ Success`);
    } catch (error) {
      console.log(`    ❌ Failed: ${error.message}`);
    }
  }
}

function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach((file) => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

function generateTitle(filename) {
  return filename
    .replace(/[_-]/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

function generateId(fileName, category) {
  const sanitized = fileName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, "_")
    .replace(/_+/g, "_")
    .replace(/^_|_$/g, "");

  return `${category.toLowerCase()}_${sanitized}_${Date.now()}`;
}

// Run the upload
uploadContent();
